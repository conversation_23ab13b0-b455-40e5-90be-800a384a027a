package main

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"slices"
	"strconv"
	"time"
)

// ----- 用户申请充值相关 -----

// UserRechargeRequest 用户充值申请请求
type UserRechargeRequest struct {
	Amount       int    `json:"amount"`        // 充值金额（人民币,单位：元）
	OrderID      string `json:"order_id"`      // 支付宝订单号
	PaymentProof string `json:"payment_proof"` // 支付凭证URL
}

// UserRechargeResponse 用户充值申请响应
type UserRechargeResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message,omitempty"`
}

// WebhookRequest 飞书Webhook请求结构
type WebhookRequest struct {
	Content WebhookContent `json:"content"`
	MsgType string         `json:"msg_type"`
}

// WebhookContent 飞书Webhook内容结构
type WebhookContent struct {
	Balance string `json:"balance"` // 用户余额
	ID      string `json:"id"`      // 用户ID
	Order   string `json:"order"`   // 支付宝订单号
	Time    string `json:"time"`    // 时间
}

// HandleUserRechargeRequest 处理用户充值申请
func HandleUserRechargeRequest(w http.ResponseWriter, r *http.Request) {
	// 只接受POST请求
	if r.Method != http.MethodPost {
		http.Error(w, "方法不允许", http.StatusMethodNotAllowed)
		return
	}
	// 从上下文中获取用户信息
	user, ok := UserFromContext(r.Context())
	if !ok {
		http.Error(w, "未授权", http.StatusUnauthorized)
		return
	}
	// 解析请求体
	var req UserRechargeRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "无效的请求格式", http.StatusBadRequest)
		return
	}
	// 验证请求参数
	if req.Amount <= 0 {
		http.Error(w, "充值金额必须为正数", http.StatusBadRequest)
		return
	}
	if req.OrderID == "" {
		http.Error(w, "支付宝订单号不能为空", http.StatusBadRequest)
		return
	}
	// 创建充值申请
	err := services.DB.CreateRechargeRequest(user.ID.String(), req.Amount, req.OrderID, req.PaymentProof)
	if err != nil {
		http.Error(w, "创建充值申请失败: "+err.Error(), http.StatusInternalServerError)
		return
	}
	// 发送飞书通知
	SendFeishuRechargeNotification(user.ID.String(), req.OrderID, req.Amount)
	// 返回成功响应
	resp := UserRechargeResponse{
		Success: true,
		Message: "充值申请已提交",
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(resp)
}

// HandleGetUserRechargeRequests 处理获取用户充值申请列表请求
func HandleGetUserRechargeRequests(w http.ResponseWriter, r *http.Request) {
	// 只接受GET请求
	if r.Method != http.MethodGet {
		http.Error(w, "方法不允许", http.StatusMethodNotAllowed)
		return
	}
	// 从上下文中获取用户信息
	user, ok := UserFromContext(r.Context())
	if !ok {
		http.Error(w, "未授权", http.StatusUnauthorized)
		return
	}
	// 获取分页参数
	limit := 10
	offset := 0
	if limitStr := r.URL.Query().Get("limit"); limitStr != "" {
		if limitVal, err := strconv.Atoi(limitStr); err == nil && limitVal > 0 {
			limit = limitVal
		}
	}
	if offsetStr := r.URL.Query().Get("offset"); offsetStr != "" {
		if offsetVal, err := strconv.Atoi(offsetStr); err == nil && offsetVal >= 0 {
			offset = offsetVal
		}
	}
	// 获取用户充值申请列表
	requests, err := services.DB.GetUserRechargeRequests(user.ID.String(), limit, offset)
	if err != nil {
		http.Error(w, "获取充值申请列表失败: "+err.Error(), http.StatusInternalServerError)
		return
	}
	// 返回充值申请列表
	resp := map[string]any{
		"success":  true,
		"requests": requests,
		"count":    len(requests),
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(resp)
}

// ----- 管理员充值管理相关 -----

// ProcessRechargeRequestRequest 处理充值申请请求
type ProcessRechargeRequestRequest struct {
	RequestID int64  `json:"request_id"` // 充值申请ID
	Action    string `json:"action"`     // 操作类型：approve或reject
	Note      string `json:"note"`       // 处理备注
	RmbAmount int    `json:"rmb_amount"` // 充值金额（人民币）
}

// AdminRechargeListResponse 管理员充值申请列表响应
type AdminRechargeListResponse struct {
	Success  bool              `json:"success"`
	Requests []RechargeRequest `json:"requests"`
	Count    int               `json:"count"`
}

// isAdmin 检查用户是否为管理员
func isAdmin(userID string) bool {
	// 从配置中获取管理员ID列表检查
	adminList := GetAdminsList()
	return slices.Contains(adminList, userID)
}

// HandleAdminGetRechargeRequests 处理管理员获取充值申请列表请求
func HandleAdminGetRechargeRequests(w http.ResponseWriter, r *http.Request) {
	// 只接受GET请求
	if r.Method != http.MethodGet {
		http.Error(w, "方法不允许", http.StatusMethodNotAllowed)
		return
	}
	// 从上下文中获取用户信息
	user, ok := UserFromContext(r.Context())
	if !ok {
		http.Error(w, "未授权", http.StatusUnauthorized)
		return
	}
	// 检查管理员权限
	if !isAdmin(user.ID.String()) {
		http.Error(w, "权限不足", http.StatusForbidden)
		return
	}
	// 获取查询参数
	status := r.URL.Query().Get("status")
	limit := 50
	offset := 0
	if limitStr := r.URL.Query().Get("limit"); limitStr != "" {
		if limitVal, err := strconv.Atoi(limitStr); err == nil && limitVal > 0 {
			limit = limitVal
		}
	}
	if offsetStr := r.URL.Query().Get("offset"); offsetStr != "" {
		if offsetVal, err := strconv.Atoi(offsetStr); err == nil && offsetVal >= 0 {
			offset = offsetVal
		}
	}
	// 获取充值申请列表
	requests, err := services.DB.GetRechargeRequests(status, limit, offset)
	if err != nil {
		http.Error(w, "获取充值申请列表失败: "+err.Error(), http.StatusInternalServerError)
		return
	}
	// 返回充值申请列表
	resp := AdminRechargeListResponse{
		Success:  true,
		Requests: requests,
		Count:    len(requests),
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(resp)
}

// HandleAdminProcessRechargeRequest 处理管理员处理充值申请请求
// 注意：充值申请表中的金额是人民币金额，而钱包中的余额是积分
// 换算关系为：1元人民币 = 1000积分
func HandleAdminProcessRechargeRequest(w http.ResponseWriter, r *http.Request) {
	// 只接受POST请求
	if r.Method != http.MethodPost {
		http.Error(w, "方法不允许", http.StatusMethodNotAllowed)
		return
	}
	// 从上下文中获取用户信息
	user, ok := UserFromContext(r.Context())
	if !ok {
		http.Error(w, "未授权", http.StatusUnauthorized)
		return
	}
	// 检查管理员权限
	if !isAdmin(user.ID.String()) {
		http.Error(w, "权限不足", http.StatusForbidden)
		return
	}
	// 解析请求体
	var req ProcessRechargeRequestRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "无效的请求格式", http.StatusBadRequest)
		return
	}
	// 验证请求参数
	if req.RequestID <= 0 {
		http.Error(w, "无效的充值申请ID", http.StatusBadRequest)
		return
	}
	if req.Action != "approve" && req.Action != "reject" {
		http.Error(w, "无效的操作类型，必须是 'approve' 或 'reject'", http.StatusBadRequest)
		return
	}
	// 处理充值申请
	err := services.DB.ProcessRechargeRequest(req.RequestID, user.ID.String(), req.Action, req.Note, req.RmbAmount)
	if err != nil {
		http.Error(w, "处理充值申请失败: "+err.Error(), http.StatusInternalServerError)
		return
	}
	// 返回成功响应
	resp := map[string]any{
		"success": true,
		"message": "充值申请处理成功",
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(resp)
}

// ----- 用户信息与余额管理相关 -----

// AdjustBalanceRequest 管理员调整用户余额请求
type AdjustBalanceRequest struct {
	UserID string `json:"user_id"` // 要调整余额的用户ID
	Amount int    `json:"amount"`  // 调整积分数量，正数为增加，负数为减少
	Reason string `json:"reason"`  // 调整原因
}

// HandleAdminGetUserInfo 处理管理员获取用户信息请求
func HandleAdminGetUserInfo(w http.ResponseWriter, r *http.Request) {
	// 只接受GET请求
	if r.Method != http.MethodGet {
		http.Error(w, "方法不允许", http.StatusMethodNotAllowed)
		return
	}
	// 从上下文中获取用户信息
	user, ok := UserFromContext(r.Context())
	if !ok {
		http.Error(w, "未授权", http.StatusUnauthorized)
		return
	}
	// 检查管理员权限
	if !isAdmin(user.ID.String()) {
		http.Error(w, "权限不足", http.StatusForbidden)
		return
	}
	// 获取要查询的用户ID
	userID := r.URL.Query().Get("user_id")
	if userID == "" {
		http.Error(w, "用户ID不能为空", http.StatusBadRequest)
		return
	}
	// 获取用户信息
	userInfo, err := services.DB.GetUserInfo(userID)
	if err != nil {
		http.Error(w, "获取用户信息失败: "+err.Error(), http.StatusInternalServerError)
		return
	}
	// 返回用户信息
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(userInfo)
}

// HandleAdminAdjustBalance 处理管理员调整用户余额请求
// 注意：这里的余额调整是直接操作积分数量，不是人民币金额
func HandleAdminAdjustBalance(w http.ResponseWriter, r *http.Request) {
	// 只接受POST请求
	if r.Method != http.MethodPost {
		http.Error(w, "方法不允许", http.StatusMethodNotAllowed)
		return
	}
	// 从上下文中获取用户信息
	user, ok := UserFromContext(r.Context())
	if !ok {
		http.Error(w, "未授权", http.StatusUnauthorized)
		return
	}
	// 检查管理员权限
	if !isAdmin(user.ID.String()) {
		http.Error(w, "权限不足", http.StatusForbidden)
		return
	}
	// 解析请求体
	var req AdjustBalanceRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "无效的请求格式", http.StatusBadRequest)
		return
	}
	// 验证请求参数
	if req.UserID == "" {
		http.Error(w, "用户ID不能为空", http.StatusBadRequest)
		return
	}
	if req.Amount == 0 {
		http.Error(w, "调整积分数量不能为0", http.StatusBadRequest)
		return
	}
	// 调整用户余额
	newBalance, err := services.DB.ManualAdjustBalance(req.UserID, req.Amount, user.ID.String(), req.Reason)
	if err != nil {
		http.Error(w, "调整用户余额失败: "+err.Error(), http.StatusInternalServerError)
		return
	}
	// 返回成功响应
	resp := map[string]any{
		"success":     true,
		"new_balance": newBalance,
		"message":     "用户余额调整成功",
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(resp)
}

// AdminProcessByOrderIDRequest 管理员根据订单号处理充值申请请求
type AdminProcessByOrderIDRequest struct {
	OrderID   string `json:"order_id"`   // 支付宝订单号
	RmbAmount int    `json:"rmb_amount"` // 充值金额（人民币）
	Note      string `json:"note"`       // 处理备注
}

// AdminProcessByOrderIDResponse 管理员根据订单号处理充值申请响应
type AdminProcessByOrderIDResponse struct {
	Success     bool             `json:"success"`
	RechargeID  int64            `json:"recharge_id,omitempty"`
	RequestInfo *RechargeRequest `json:"request_info,omitempty"`
	PointsAdded int              `json:"points_added,omitempty"` // 添加的积分数量
	NewBalance  int              `json:"new_balance,omitempty"`  // 用户新余额
	Message     string           `json:"message,omitempty"`
}

// ----- 交易记录与统计分析相关 -----

// TransactionRecordsRequest 交易记录查询请求
type TransactionRecordsRequest struct {
	UserID    string `json:"user_id,omitempty"`    // 用户ID（可选）
	Type      string `json:"type,omitempty"`       // 交易类型（可选）
	StartTime int64  `json:"start_time,omitempty"` // 开始时间戳（可选）
	EndTime   int64  `json:"end_time,omitempty"`   // 结束时间戳（可选）
	Limit     int    `json:"limit,omitempty"`      // 限制数量（可选）
	Offset    int    `json:"offset,omitempty"`     // 偏移量（可选）
}

// HandleAdminGetTransactionRecords 处理管理员获取交易记录请求
func HandleAdminGetTransactionRecords(w http.ResponseWriter, r *http.Request) {
	// 只接受POST请求
	if r.Method != http.MethodPost {
		http.Error(w, "方法不允许", http.StatusMethodNotAllowed)
		return
	}
	// 从上下文中获取用户信息
	user, ok := UserFromContext(r.Context())
	if !ok {
		http.Error(w, "未授权", http.StatusUnauthorized)
		return
	}
	// 检查管理员权限
	if !isAdmin(user.ID.String()) {
		http.Error(w, "权限不足", http.StatusForbidden)
		return
	}
	// 解析请求体
	var req TransactionRecordsRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "无效的请求格式", http.StatusBadRequest)
		return
	}
	// 设置默认值
	if req.Limit <= 0 {
		req.Limit = 50
	}
	if req.Limit > 200 {
		req.Limit = 200 // 限制最大查询数量
	}
	if req.Offset < 0 {
		req.Offset = 0
	}
	// 将int64时间戳转换为time.Time
	var startTime, endTime time.Time
	if req.StartTime > 0 {
		startTime = time.Unix(req.StartTime, 0)
	}
	if req.EndTime > 0 {
		endTime = time.Unix(req.EndTime, 0)
	}
	// 获取交易记录
	transactions, err := services.DB.GetTransactionRecords(req.UserID, req.Type, startTime, endTime, req.Limit, req.Offset)
	if err != nil {
		http.Error(w, "获取交易记录失败: "+err.Error(), http.StatusInternalServerError)
		return
	}
	// 返回交易记录
	resp := map[string]any{
		"success":      true,
		"transactions": transactions,
		"count":        len(transactions),
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(resp)
}

// StatisticsRequest 统计数据请求
type StatisticsRequest struct {
	StartDate string `json:"start_date"` // 开始日期 YYYY-MM-DD
	EndDate   string `json:"end_date"`   // 结束日期 YYYY-MM-DD
}

// HandleAdminGetStatistics 处理管理员获取统计数据请求
func HandleAdminGetStatistics(w http.ResponseWriter, r *http.Request) {
	// 只接受POST请求
	if r.Method != http.MethodPost {
		http.Error(w, "方法不允许", http.StatusMethodNotAllowed)
		return
	}
	// 从上下文中获取用户信息
	user, ok := UserFromContext(r.Context())
	if !ok {
		http.Error(w, "未授权", http.StatusUnauthorized)
		return
	}
	// 检查管理员权限
	if !isAdmin(user.ID.String()) {
		http.Error(w, "权限不足", http.StatusForbidden)
		return
	}
	// 解析请求体
	var req StatisticsRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "无效的请求格式", http.StatusBadRequest)
		return
	}
	// 设置默认值
	if req.StartDate == "" {
		// 默认为30天前
		req.StartDate = time.Now().AddDate(0, 0, -30).Format("2006-01-02")
	}
	if req.EndDate == "" {
		// 默认为今天
		req.EndDate = time.Now().Format("2006-01-02")
	}
	// 获取统计数据
	stats, err := services.DB.GetRechargeStatistics(req.StartDate, req.EndDate)
	if err != nil {
		http.Error(w, "获取统计数据失败: "+err.Error(), http.StatusInternalServerError)
		return
	}
	// 返回统计数据
	resp := map[string]any{
		"success":    true,
		"statistics": stats,
		"count":      len(stats),
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(resp)
}

// SendFeishuRechargeNotification 发送充值申请通知到飞书
func SendFeishuRechargeNotification(userID string, orderID string, amount int) {
	webhookURL := GetFeishuWebhookURL()
	if webhookURL == "" {
		LogWarn("未配置飞书Webhook URL，无法发送通知")
		return
	}
	// 构建通知内容
	content := WebhookContent{
		Balance: strconv.Itoa(amount),
		ID:      userID,
		Order:   orderID,
		Time:    time.Now().Format("2006-01-02 15:04:05"),
	}
	// 构建请求体
	request := WebhookRequest{
		Content: content,
		MsgType: "text",
	}
	// 序列化请求体
	requestBody, err := json.Marshal(request)
	if err != nil {
		LogError("飞书通知，序列化请求体失败: %v", err)
		return
	}
	// 发送POST请求
	resp, err := http.Post(webhookURL, "application/json", bytes.NewBuffer(requestBody))
	if err != nil {
		LogError("飞书通知，发送请求失败: %v", err)
		return
	}
	defer resp.Body.Close()
	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		LogError("飞书通知，发送请求失败，状态码: %d 响应: %s", resp.StatusCode, string(body))
		return
	}
}
